/**
 * Cache warming system for proactive cache management
 */

import {getPlatformConfig} from '@common/platform/ssr';

interface WarmingResult {
  domain: string;
  success: boolean;
  duration: number;
  error?: string;
}

interface WarmingStats {
  totalDomains: number;
  successful: number;
  failed: number;
  totalDuration: number;
  averageDuration: number;
  errors: string[];
}

export class CacheWarmer {
  private popularDomains: string[] = [];
  private isWarming = false;
  private lastWarmingTime = 0;
  private warmingInterval = 6 * 60 * 60 * 1000; // 6 hours

  constructor(popularDomains: string[] = []) {
    this.popularDomains = popularDomains;
  }

  /**
   * Set the list of popular domains to warm
   */
  setPopularDomains(domains: string[]): void {
    this.popularDomains = domains;
    console.log(`[CACHE_WARM] Updated popular domains list: ${domains.length} domains`);
  }

  /**
   * Add a domain to the popular domains list
   */
  addPopularDomain(domain: string): void {
    if (!this.popularDomains.includes(domain)) {
      this.popularDomains.push(domain);
      console.log(`[CACHE_WARM] Added domain to popular list: ${domain}`);
    }
  }

  /**
   * Remove a domain from the popular domains list
   */
  removePopularDomain(domain: string): void {
    const index = this.popularDomains.indexOf(domain);
    if (index > -1) {
      this.popularDomains.splice(index, 1);
      console.log(`[CACHE_WARM] Removed domain from popular list: ${domain}`);
    }
  }

  /**
   * Warm platform configs for all popular domains
   */
  async warmPlatformConfigs(): Promise<WarmingStats> {
    if (this.isWarming) {
      throw new Error('Cache warming is already in progress');
    }

    this.isWarming = true;
    const startTime = Date.now();
    
    console.log(`[CACHE_WARM] Starting platform config warming for ${this.popularDomains.length} domains...`);

    try {
      const results = await Promise.allSettled(
        this.popularDomains.map(async (domain): Promise<WarmingResult> => {
          const domainStartTime = Date.now();
          
          try {
            await getPlatformConfig(domain);
            const duration = Date.now() - domainStartTime;
            
            console.log(`[CACHE_WARM] ✅ ${domain} warmed in ${duration}ms`);
            
            return {
              domain,
              success: true,
              duration,
            };
          } catch (error) {
            const duration = Date.now() - domainStartTime;
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            
            console.log(`[CACHE_WARM] ❌ ${domain} failed in ${duration}ms: ${errorMessage}`);
            
            return {
              domain,
              success: false,
              duration,
              error: errorMessage,
            };
          }
        })
      );

      const warmingResults = results.map(result => 
        result.status === 'fulfilled' ? result.value : {
          domain: 'unknown',
          success: false,
          duration: 0,
          error: result.reason?.message || 'Promise rejected'
        }
      );

      const stats = this.calculateStats(warmingResults);
      const totalDuration = Date.now() - startTime;

      console.log(`[CACHE_WARM] Platform config warming completed in ${totalDuration}ms:`);
      console.log(`[CACHE_WARM] Success: ${stats.successful}/${stats.totalDomains} domains`);
      console.log(`[CACHE_WARM] Average duration: ${stats.averageDuration}ms`);
      
      if (stats.failed > 0) {
        console.log(`[CACHE_WARM] Failures: ${stats.errors.join(', ')}`);
      }

      this.lastWarmingTime = Date.now();
      return stats;

    } finally {
      this.isWarming = false;
    }
  }

  /**
   * Warm cache for a specific domain
   */
  async warmDomain(domain: string): Promise<WarmingResult> {
    const startTime = Date.now();
    
    try {
      console.log(`[CACHE_WARM] Warming cache for ${domain}...`);
      
      // Warm platform config
      await getPlatformConfig(domain);
      
      const duration = Date.now() - startTime;
      console.log(`[CACHE_WARM] ✅ ${domain} warmed successfully in ${duration}ms`);
      
      return {
        domain,
        success: true,
        duration,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.log(`[CACHE_WARM] ❌ ${domain} warming failed in ${duration}ms: ${errorMessage}`);
      
      return {
        domain,
        success: false,
        duration,
        error: errorMessage,
      };
    }
  }

  /**
   * Start automatic cache warming on an interval
   */
  startAutoWarming(): void {
    if (this.popularDomains.length === 0) {
      console.warn('[CACHE_WARM] No popular domains configured for auto warming');
      return;
    }

    console.log(`[CACHE_WARM] Starting auto warming every ${this.warmingInterval / 1000 / 60} minutes`);

    // Initial warming
    this.warmPlatformConfigs().catch(error => {
      console.error('[CACHE_WARM] Initial warming failed:', error);
    });

    // Set up interval
    setInterval(() => {
      if (!this.isWarming) {
        this.warmPlatformConfigs().catch(error => {
          console.error('[CACHE_WARM] Scheduled warming failed:', error);
        });
      } else {
        console.log('[CACHE_WARM] Skipping scheduled warming - already in progress');
      }
    }, this.warmingInterval);
  }

  /**
   * Check if warming is needed based on last warming time
   */
  isWarmingNeeded(): boolean {
    const timeSinceLastWarming = Date.now() - this.lastWarmingTime;
    return timeSinceLastWarming >= this.warmingInterval;
  }

  /**
   * Get warming status
   */
  getStatus(): {
    isWarming: boolean;
    lastWarmingTime: number;
    timeSinceLastWarming: number;
    isWarmingNeeded: boolean;
    popularDomainsCount: number;
  } {
    return {
      isWarming: this.isWarming,
      lastWarmingTime: this.lastWarmingTime,
      timeSinceLastWarming: Date.now() - this.lastWarmingTime,
      isWarmingNeeded: this.isWarmingNeeded(),
      popularDomainsCount: this.popularDomains.length,
    };
  }

  /**
   * Calculate statistics from warming results
   */
  private calculateStats(results: WarmingResult[]): WarmingStats {
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

    return {
      totalDomains: results.length,
      successful: successful.length,
      failed: failed.length,
      totalDuration,
      averageDuration: results.length > 0 ? Math.round(totalDuration / results.length) : 0,
      errors: failed.map(r => `${r.domain}: ${r.error}`),
    };
  }
}

// Global cache warmer instance
export const globalCacheWarmer = new CacheWarmer();

// Initialize with some default popular domains (can be configured via env)
const defaultPopularDomains = process.env.POPULAR_DOMAINS?.split(',') || [];
if (defaultPopularDomains.length > 0) {
  globalCacheWarmer.setPopularDomains(defaultPopularDomains);
}

// Auto-start warming in production
if (process.env.NODE_ENV === 'production' && typeof window === 'undefined') {
  // Start auto warming after a short delay to allow app to initialize
  setTimeout(() => {
    globalCacheWarmer.startAutoWarming();
  }, 30000); // 30 seconds delay
}

export default CacheWarmer;
