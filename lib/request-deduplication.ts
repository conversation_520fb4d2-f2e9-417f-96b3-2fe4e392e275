/**
 * Request deduplication utility to prevent duplicate API calls
 * within a specified time window
 */

interface PendingRequest<T> {
  promise: Promise<T>;
  timestamp: number;
  cleanup: () => void;
}

class RequestDeduplicator {
  private pendingRequests = new Map<string, PendingRequest<any>>();
  private defaultTTL: number;

  constructor(defaultTTL: number = 5000) { // 5 seconds default
    this.defaultTTL = defaultTTL;
  }

  /**
   * Execute a function with deduplication
   * If the same key is already being processed, return the existing promise
   */
  async dedupe<T>(
    key: string,
    fn: () => Promise<T>,
    ttl: number = this.defaultTTL
  ): Promise<T> {
    const now = Date.now();
    const existing = this.pendingRequests.get(key);

    // Check if we have a valid pending request
    if (existing && (now - existing.timestamp) < ttl) {
      console.log(`[DEDUPE] Cache hit for key: ${key}`);
      return existing.promise;
    }

    // Clean up expired request if exists
    if (existing) {
      existing.cleanup();
      this.pendingRequests.delete(key);
    }

    console.log(`[DEDUPE] Cache miss for key: ${key}, executing function`);

    // Create cleanup function
    const cleanup = () => {
      this.pendingRequests.delete(key);
    };

    // Create new promise with automatic cleanup
    const promise = fn().finally(() => {
      // Clean up after TTL expires
      setTimeout(cleanup, ttl);
    });

    // Store the pending request
    this.pendingRequests.set(key, {
      promise,
      timestamp: now,
      cleanup,
    });

    return promise;
  }

  /**
   * Clear all pending requests (useful for testing)
   */
  clear(): void {
    for (const [key, request] of this.pendingRequests) {
      request.cleanup();
    }
    this.pendingRequests.clear();
  }

  /**
   * Get stats about pending requests
   */
  getStats(): { pendingCount: number; keys: string[] } {
    return {
      pendingCount: this.pendingRequests.size,
      keys: Array.from(this.pendingRequests.keys()),
    };
  }

  /**
   * Force cleanup of expired requests
   */
  cleanupExpired(ttl: number = this.defaultTTL): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, request] of this.pendingRequests) {
      if ((now - request.timestamp) >= ttl) {
        request.cleanup();
        this.pendingRequests.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      console.log(`[DEDUPE] Cleaned up ${cleaned} expired requests`);
    }

    return cleaned;
  }
}

// Global deduplicator instances
export const firebaseDeduplicator = new RequestDeduplicator(5000); // 5 seconds for Firebase
export const platformDeduplicator = new RequestDeduplicator(3000); // 3 seconds for Platform API
export const generalDeduplicator = new RequestDeduplicator(2000);  // 2 seconds for general use

/**
 * Convenience function for Firebase requests
 */
export function dedupeFirebaseRequest<T>(
  key: string,
  fn: () => Promise<T>
): Promise<T> {
  return firebaseDeduplicator.dedupe(key, fn);
}

/**
 * Convenience function for Platform API requests
 */
export function dedupePlatformRequest<T>(
  key: string,
  fn: () => Promise<T>
): Promise<T> {
  return platformDeduplicator.dedupe(key, fn);
}

/**
 * Convenience function for general requests
 */
export function dedupeRequest<T>(
  key: string,
  fn: () => Promise<T>,
  ttl?: number
): Promise<T> {
  return generalDeduplicator.dedupe(key, fn, ttl);
}

/**
 * Create a deduped version of any async function
 */
export function createDedupedFunction<TArgs extends any[], TReturn>(
  fn: (...args: TArgs) => Promise<TReturn>,
  keyGenerator: (...args: TArgs) => string,
  ttl?: number
): (...args: TArgs) => Promise<TReturn> {
  return (...args: TArgs) => {
    const key = keyGenerator(...args);
    return generalDeduplicator.dedupe(key, () => fn(...args), ttl);
  };
}

/**
 * Periodic cleanup of expired requests
 */
if (typeof window === 'undefined') { // Server-side only
  setInterval(() => {
    firebaseDeduplicator.cleanupExpired();
    platformDeduplicator.cleanupExpired();
    generalDeduplicator.cleanupExpired();
  }, 30000); // Clean up every 30 seconds
}

export default RequestDeduplicator;
