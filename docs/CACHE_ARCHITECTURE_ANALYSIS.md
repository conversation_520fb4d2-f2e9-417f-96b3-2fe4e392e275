# 🏗️ **Cache Architecture Analysis - TrueStore Frontend**

## 📋 **Document Overview**

| Attribute | Value |
|-----------|-------|
| **Document Type** | Technical Architecture Analysis |
| **System** | TrueStore Frontend Cache Layer |
| **Current Scale** | 10,000 CCU, 100+ Domains |
| **Technology Stack** | Next.js 14, Redis, @neshca/cache-handler |
| **Analysis Date** | 2025-06-18 |
| **Severity Level** | 🚨 **CRITICAL** |

---

## 🎯 **Executive Summary**

### **Current State**
- ❌ **Nuclear cache invalidation** via `'all'` tag affecting entire system
- ❌ **Cache stampede** scenarios causing 2-5s response times
- ❌ **Unprotected revalidation API** exposing system to abuse
- ❌ **Firebase API overload** during cache misses (200+ concurrent calls)

### **Proposed Solution**
- ✅ **Granular tag-based invalidation** for surgical cache updates
- ✅ **Domain-specific caching** to isolate cache impacts
- ✅ **Protected revalidation endpoints** with authentication & rate limiting
- ✅ **Smart cache warming** and monitoring for proactive management

### **Expected Impact**
- 🚀 **95%+ cache hit rate** (vs current ~60-70%)
- 🚀 **P99 < 100ms** (vs current 2-5s during invalidation)
- 🚀 **Zero cache storms** (vs current frequent storms)
- 🚀 **Predictable performance** across all domains

---

## 🏗️ **1. CURRENT ARCHITECTURE**

### **1.1 Cache Handler Setup**

```mermaid
graph TB
    A[Next.js App] --> B[Cache Handler]
    B --> C[Redis Client]
    C --> D[Redis Server]
    
    E[Cache Key Structure] --> F["next-cache:{buildId}:{hash}"]
    G[TTL Strategy] --> H[30 days default]
    I[Expiration] --> J[EXAT strategy]
```

**Implementation:**
```javascript
// cache-handler.mjs
const handler = await createRedisHandler({
  client,
  keyPrefix: `next-cache:${buildId}`,
  timeoutMs: 1000,
  keyExpirationStrategy: 'EXAT',
});

return {
  handlers: [handler],
  ttl: {
    estimateExpireAge: () => ms('30 days') / 1000,
  },
};
```

### **1.2 Cache Tag Distribution**

```mermaid
pie title Cache Tag Usage
    "'all'" : 4
    "'platform-config'" : 1
    "'platform-pixel'" : 1
    "'product'" : 1
    "'reviews'" : 1
```

| Function | Tags | Revalidate | Impact Scope |
|----------|------|------------|--------------|
| `getPlatformConfig()` | `['platform-config', 'all']` | 86400s | 🚨 **100+ domains** |
| `getPlatformPixel()` | `['platform-pixel', 'all']` | 86400s | 🚨 **Global data** |
| `getProduct()` | `['product', 'all']` | 86400s | 🚨 **All products** |
| `getProductReviews()` | `['reviews', 'all']` | 86400s | 🚨 **All reviews** |

### **1.3 Current Data Flow**

```mermaid
sequenceDiagram
    participant C as Client
    participant N as Next.js
    participant R as Redis
    participant F as Firebase
    participant P as Platform API

    C->>N: Request /domain.com/product
    N->>R: Check cache
    
    alt Cache Hit
        R-->>N: Return cached data
        N-->>C: Fast response (50ms)
    else Cache Miss
        N->>F: getPlatformConfig()
        N->>F: getPlatformPixel()
        N->>P: getProduct()
        F-->>N: Platform config (100ms)
        F-->>N: Pixel data (50ms)
        P-->>N: Product data (80ms)
        N->>R: Store with tags ['all', ...]
        N-->>C: Slow response (300ms)
    end
```

---

## 🚨 **2. CRITICAL PROBLEMS**

### **2.1 Nuclear Cache Invalidation**

**Problem:**
```typescript
// Single call destroys ALL cache
revalidateTag('all'); 

// Affected data:
// - 100+ domain configurations
// - Thousands of products  
// - All reviews and ratings
// - Global pixel configurations
```

**Impact Scenario:**
```
Timeline: Cache invalidation event
T+0s:    revalidateTag('all') called
T+1s:    ALL cache entries marked invalid
T+2s:    1000+ concurrent requests hit cache miss
T+3s:    200+ Firebase API calls triggered
T+5s:    Firebase rate limiting kicks in
T+10s:   Error rate spikes to 30%
T+30s:   System gradually recovers
```

### **2.2 Cache Stampede Analysis**

**Scenario: 10k CCU across 100 domains**

```mermaid
graph TD
    A[revalidateTag 'all'] --> B[All cache invalid]
    B --> C[1000 concurrent requests]
    C --> D[100 domains × 2 Firebase calls]
    D --> E[200+ concurrent API calls]
    E --> F[Firebase rate limiting]
    F --> G[Response time: 2-5 seconds]
    G --> H[Error rate: 15-30%]
```

**Mathematical Impact:**
```
Concurrent Users: 10,000
Active Domains: 100
Cache Miss Rate: 100% (after 'all' invalidation)

Firebase Calls = Domains × API_calls_per_domain × Concurrent_factor
Firebase Calls = 100 × 2 × 1.5 = 300 concurrent calls

Expected Response Time:
- Normal: 50-100ms
- During stampede: 2,000-5,000ms
- Recovery time: 30-60 seconds
```

### **2.3 Unprotected Revalidation API**

**Security Issues:**
```typescript
// app/api/revalidate/route.ts
export async function GET(request: NextRequest) {
  const tag = request.nextUrl.searchParams.get('tag');
  
  if (tag) {
    revalidateTag(tag); // ❌ No authentication
  }
  // ❌ No rate limiting
  // ❌ No input validation
  // ❌ No audit logging
}
```

**Attack Vectors:**
- **DoS via cache clearing**: `GET /api/revalidate?tag=all`
- **Resource exhaustion**: Repeated invalidation calls
- **Performance degradation**: Malicious cache clearing

### **2.4 Firebase API Bottleneck**

**Current API Pattern:**
```typescript
// Every cache miss triggers 2 Firebase calls
const [platform, commonPixelIds] = await Promise.all([
  ky.get(`PXTRUE2/${domain}/.json`).json(), // 40-120ms
  getPlatformPixel(),                        // 30-60ms (cached)
]);
```

**Bottleneck Analysis:**
- **Single point of failure**: Firebase database
- **Rate limiting**: 1000 concurrent connections max
- **Geographic latency**: Variable based on user location
- **No fallback mechanism**: System fails if Firebase is down

---

## 📊 **3. PERFORMANCE METRICS**

### **3.1 Current Performance**

| Metric | Normal Operation | During Cache Storm |
|--------|------------------|-------------------|
| **Response Time P50** | 80ms | 1,200ms |
| **Response Time P95** | 150ms | 3,500ms |
| **Response Time P99** | 300ms | 5,000ms |
| **Error Rate** | <1% | 15-30% |
| **Cache Hit Rate** | 70% | 0% (post-invalidation) |
| **Firebase Calls/min** | 50-100 | 2,000+ |

### **3.2 Resource Utilization**

```mermaid
graph LR
    A[Normal Load] --> B[CPU: 20%]
    A --> C[Memory: 40%]
    A --> D[Network: 30%]
    
    E[Cache Storm] --> F[CPU: 80%]
    E --> G[Memory: 90%]
    E --> H[Network: 95%]
```

### **3.3 Business Impact**

**Revenue Impact:**
- **Conversion drop**: 25-40% during cache storms
- **User abandonment**: 60% for responses >3s
- **SEO impact**: Core Web Vitals degradation

**Operational Impact:**
- **Support tickets**: 300% increase during incidents
- **Engineering time**: 20 hours/month on cache issues
- **Infrastructure costs**: Over-provisioning to handle spikes

---

## 🎯 **4. ROOT CAUSE ANALYSIS**

### **4.1 Design Flaws**

1. **Over-broad tagging**: `'all'` tag creates unnecessary coupling
2. **Lack of isolation**: Domain data not isolated from global data
3. **No graceful degradation**: System fails hard on cache miss
4. **Missing monitoring**: No visibility into cache performance

### **4.2 Implementation Issues**

1. **Synchronous invalidation**: No background refresh capability
2. **No cache warming**: Cold starts always result in poor performance
3. **Missing circuit breakers**: No protection against API failures
4. **Inadequate error handling**: Failures cascade through system

### **4.3 Operational Gaps**

1. **No alerting**: Cache storms go undetected
2. **Manual intervention**: No automated recovery mechanisms
3. **Limited observability**: Difficult to debug cache issues
4. **No capacity planning**: Unable to predict cache requirements

---

## 🚀 **5. PROPOSED NEW ARCHITECTURE**

### **5.1 Granular Tag Strategy**

**New Tag Hierarchy:**
```mermaid
graph TD
    A[Global Tags] --> B[platform]
    A --> C[product]
    A --> D[reviews]

    B --> E[platform:domain.com]
    B --> F[platform:config]
    B --> G[platform:pixel]

    C --> H[product:domain.com]
    C --> I[product:domain.com:category]
    C --> J[product:domain.com:product-slug]

    D --> K[reviews:domain.com]
    D --> L[reviews:domain.com:product-id]
```

**Implementation:**
```typescript
// ✅ NEW: Domain-specific platform config
export const getPlatformConfig = cache(
  async (domain: string) => {
    // ... implementation
  },
  [],
  {
    revalidate: 86400,
    tags: [
      `platform:${domain}`,           // Domain-specific
      `platform:config`,              // Type-specific
      'platform'                      // Global category
    ],
  },
);

// ✅ NEW: Global pixel data (no domain coupling)
export const getPlatformPixel = cache(
  async () => {
    // ... implementation
  },
  [],
  {
    revalidate: 86400,
    tags: [
      'platform:pixel',               // Type-specific
      'platform'                      // Global category
    ],
  },
);

// ✅ NEW: Product-specific caching
export const getProduct = cache(
  async (domain: string, slug: string) => {
    // ... implementation
  },
  [],
  {
    revalidate: 86400,
    tags: [
      `product:${domain}:${slug}`,    // Specific product
      `product:${domain}`,            // Domain products
      'product'                       // Global category
    ],
  },
);
```

### **5.2 Smart Invalidation System**

**Selective Invalidation:**
```typescript
// ✅ Surgical invalidation examples
revalidateTag(`platform:${domain}`);           // Only one domain
revalidateTag('platform:config');              // All configs, keep pixels
revalidateTag(`product:${domain}:${slug}`);    // Single product
revalidateTag(`product:${domain}`);            // Domain products only

// ❌ REMOVED: Nuclear option
// revalidateTag('all'); // This pattern is eliminated
```

**Invalidation Impact Matrix:**
| Action | Affected Cache | Recovery Time | API Calls |
|--------|---------------|---------------|-----------|
| Update domain config | `platform:domain.com` | <100ms | 1-2 |
| Update global pixels | `platform:pixel` | <200ms | 1 |
| Update product | `product:domain.com:slug` | <50ms | 1 |
| Update domain products | `product:domain.com` | <500ms | 10-50 |

### **5.3 Protected Revalidation API**

**Enhanced Security:**
```typescript
// app/api/revalidate/route.ts
import { rateLimit } from '@/lib/rate-limit';

const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500,
});

export async function POST(request: NextRequest) {
  try {
    // 🔒 Authentication
    const authHeader = request.headers.get('authorization');
    if (authHeader !== `Bearer ${process.env.REVALIDATE_SECRET}`) {
      return new Response('Unauthorized', { status: 401 });
    }

    // 🔒 Rate limiting
    await limiter.check(request.ip, 10); // 10 requests per minute

    const { tag, tags, domain } = await request.json();

    // 🔒 Input validation
    const validTags = ['platform', 'product', 'reviews'];
    const isValidTag = (t: string) =>
      validTags.some(valid => t.startsWith(valid));

    if (tag && !isValidTag(tag)) {
      return new Response('Invalid tag', { status: 400 });
    }

    // 🔒 Audit logging
    console.log(`[CACHE_REVALIDATE] ${new Date().toISOString()}: ${tag || tags} by ${request.ip}`);

    // Selective revalidation
    if (tag) {
      revalidateTag(tag);
    }

    if (tags && Array.isArray(tags)) {
      tags.filter(isValidTag).forEach(revalidateTag);
    }

    return NextResponse.json({
      revalidated: true,
      timestamp: Date.now(),
      tag: tag || tags,
    });

  } catch (error) {
    if (error.message === 'Rate limit exceeded') {
      return new Response('Rate limit exceeded', { status: 429 });
    }

    console.error('[CACHE_REVALIDATE_ERROR]', error);
    return new Response('Internal error', { status: 500 });
  }
}
```

### **5.4 Cache Warming Strategy**

**Proactive Cache Management:**
```typescript
// lib/cache-warming.ts
export class CacheWarmer {
  private popularDomains = [
    'domain1.com', 'domain2.com', 'domain3.com'
  ];

  async warmPlatformConfigs() {
    console.log('[CACHE_WARM] Starting platform config warming...');

    const results = await Promise.allSettled(
      this.popularDomains.map(async domain => {
        try {
          await getPlatformConfig(domain);
          return { domain, status: 'success' };
        } catch (error) {
          return { domain, status: 'error', error };
        }
      })
    );

    const successful = results.filter(r => r.status === 'fulfilled').length;
    console.log(`[CACHE_WARM] Warmed ${successful}/${this.popularDomains.length} domains`);
  }

  async warmPopularProducts(domain: string, productSlugs: string[]) {
    console.log(`[CACHE_WARM] Warming ${productSlugs.length} products for ${domain}`);

    await Promise.allSettled(
      productSlugs.map(slug =>
        getProduct(domain, slug).catch(() => {}) // Silent fail
      )
    );
  }

  // Scheduled warming (run every 6 hours)
  async scheduledWarm() {
    await this.warmPlatformConfigs();

    // Warm top products for each domain
    for (const domain of this.popularDomains) {
      const topProducts = await this.getTopProducts(domain);
      await this.warmPopularProducts(domain, topProducts);
    }
  }
}

// Usage in cron job or startup
const warmer = new CacheWarmer();
setInterval(() => warmer.scheduledWarm(), 6 * 60 * 60 * 1000); // Every 6 hours
```

### **5.5 Circuit Breaker Pattern**

**Resilient API Calls:**
```typescript
// lib/circuit-breaker.ts
class CircuitBreaker {
  private failures = 0;
  private lastFailTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  async execute<T>(fn: () => Promise<T>, fallback?: () => T): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailTime > 60000) { // 1 minute timeout
        this.state = 'HALF_OPEN';
      } else {
        if (fallback) return fallback();
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      if (fallback) return fallback();
      throw error;
    }
  }

  private onSuccess() {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure() {
    this.failures++;
    this.lastFailTime = Date.now();

    if (this.failures >= 5) {
      this.state = 'OPEN';
    }
  }
}

// Enhanced platform config with circuit breaker
const firebaseBreaker = new CircuitBreaker();

export const getPlatformConfigResilient = cache(
  async (domain: string) => {
    return firebaseBreaker.execute(
      () => fetchFromFirebase(domain),
      () => getDefaultPlatformConfig(domain) // Fallback
    );
  },
  [],
  {
    revalidate: 86400,
    tags: [`platform:${domain}`, 'platform:config', 'platform'],
  },
);
```

---

## 📊 **6. MONITORING & OBSERVABILITY**

### **6.1 Cache Performance Metrics**

**Key Performance Indicators:**
```typescript
// lib/cache-metrics.ts
interface CacheMetrics {
  hitRate: number;           // Cache hit percentage
  missRate: number;          // Cache miss percentage
  avgResponseTime: number;   // Average response time
  p95ResponseTime: number;   // 95th percentile response time
  errorRate: number;         // Error percentage
  invalidationCount: number; // Number of invalidations per hour
  warmingSuccess: number;    // Cache warming success rate
}

class CacheMonitor {
  private metrics: Map<string, CacheMetrics> = new Map();

  recordCacheHit(key: string, responseTime: number) {
    console.log(`[CACHE_HIT] ${key} - ${responseTime}ms`);
    this.updateMetrics(key, { hit: true, responseTime });
  }

  recordCacheMiss(key: string, responseTime: number) {
    console.log(`[CACHE_MISS] ${key} - ${responseTime}ms`);
    this.updateMetrics(key, { hit: false, responseTime });
  }

  recordInvalidation(tag: string, affectedKeys: number) {
    console.log(`[CACHE_INVALIDATE] ${tag} - ${affectedKeys} keys affected`);
    // Send to monitoring service
    this.sendToDatadog('cache.invalidation', affectedKeys, { tag });
  }

  private sendToDatadog(metric: string, value: number, tags: Record<string, string>) {
    // Integration with monitoring service
    if (process.env.DATADOG_API_KEY) {
      // Send metrics to Datadog/Grafana/etc
    }
  }
}

export const cacheMonitor = new CacheMonitor();
```

**Enhanced Cache Functions with Monitoring:**
```typescript
// Enhanced getPlatformConfig with monitoring
export const getPlatformConfig = cache(
  async (domain: string) => {
    const startTime = Date.now();
    const cacheKey = `platform:${domain}`;

    try {
      // Check if this is a cache hit/miss
      const isCacheHit = await checkCacheExists(cacheKey);

      const result = await fetchPlatformConfig(domain);
      const responseTime = Date.now() - startTime;

      if (isCacheHit) {
        cacheMonitor.recordCacheHit(cacheKey, responseTime);
      } else {
        cacheMonitor.recordCacheMiss(cacheKey, responseTime);
      }

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      cacheMonitor.recordError(cacheKey, responseTime, error);
      throw error;
    }
  },
  [],
  {
    revalidate: 86400,
    tags: [`platform:${domain}`, 'platform:config', 'platform'],
  },
);
```

### **6.2 Real-time Dashboards**

**Grafana Dashboard Configuration:**
```yaml
# grafana-cache-dashboard.yml
dashboard:
  title: "TrueStore Cache Performance"
  panels:
    - title: "Cache Hit Rate"
      type: "stat"
      targets:
        - expr: "cache_hit_rate"
        - refId: "A"
      thresholds:
        - color: "red"
          value: 0.8
        - color: "yellow"
          value: 0.9
        - color: "green"
          value: 0.95

    - title: "Response Time Distribution"
      type: "histogram"
      targets:
        - expr: "histogram_quantile(0.95, cache_response_time_bucket)"
        - expr: "histogram_quantile(0.50, cache_response_time_bucket)"

    - title: "Cache Invalidations"
      type: "graph"
      targets:
        - expr: "rate(cache_invalidations_total[5m])"

    - title: "Firebase API Calls"
      type: "graph"
      targets:
        - expr: "rate(firebase_api_calls_total[5m])"
```

**Alert Rules:**
```yaml
# prometheus-alerts.yml
groups:
  - name: cache.rules
    rules:
      - alert: CacheHitRateDropped
        expr: cache_hit_rate < 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Cache hit rate dropped below 80%"
          description: "Cache hit rate is {{ $value }}% for the last 5 minutes"

      - alert: CacheStampede
        expr: rate(cache_miss_total[1m]) > 100
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Cache stampede detected"
          description: "Cache miss rate is {{ $value }} per second"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, cache_response_time_bucket) > 1000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High cache response time"
          description: "95th percentile response time is {{ $value }}ms"

      - alert: FirebaseAPIOverload
        expr: rate(firebase_api_calls_total[1m]) > 50
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Firebase API overload"
          description: "Firebase API calls: {{ $value }} per second"
```

### **6.3 Automated Health Checks**

**Cache Health Monitor:**
```typescript
// lib/cache-health.ts
class CacheHealthChecker {
  async performHealthCheck(): Promise<HealthStatus> {
    const checks = await Promise.allSettled([
      this.checkRedisConnection(),
      this.checkCacheHitRate(),
      this.checkResponseTimes(),
      this.checkFirebaseAPI(),
    ]);

    const results = checks.map((check, index) => ({
      name: ['redis', 'hitRate', 'responseTime', 'firebase'][index],
      status: check.status === 'fulfilled' ? 'healthy' : 'unhealthy',
      details: check.status === 'fulfilled' ? check.value : check.reason,
    }));

    const overallHealth = results.every(r => r.status === 'healthy')
      ? 'healthy' : 'unhealthy';

    return {
      status: overallHealth,
      timestamp: new Date().toISOString(),
      checks: results,
    };
  }

  private async checkRedisConnection(): Promise<string> {
    try {
      // Test Redis connection
      const client = createClient({ url: process.env.NEXT_REDIS_URL });
      await client.connect();
      await client.ping();
      await client.disconnect();
      return 'Redis connection successful';
    } catch (error) {
      throw new Error(`Redis connection failed: ${error.message}`);
    }
  }

  private async checkCacheHitRate(): Promise<string> {
    const hitRate = await this.getCurrentHitRate();
    if (hitRate < 0.8) {
      throw new Error(`Cache hit rate too low: ${hitRate}%`);
    }
    return `Cache hit rate: ${hitRate}%`;
  }

  private async checkResponseTimes(): Promise<string> {
    const p95 = await this.getP95ResponseTime();
    if (p95 > 1000) {
      throw new Error(`Response time too high: ${p95}ms`);
    }
    return `P95 response time: ${p95}ms`;
  }

  private async checkFirebaseAPI(): Promise<string> {
    try {
      const testDomain = 'health-check.com';
      const start = Date.now();
      await getPlatformConfig(testDomain);
      const duration = Date.now() - start;

      if (duration > 2000) {
        throw new Error(`Firebase API slow: ${duration}ms`);
      }

      return `Firebase API healthy: ${duration}ms`;
    } catch (error) {
      throw new Error(`Firebase API failed: ${error.message}`);
    }
  }
}

// Health check endpoint
// app/api/health/cache/route.ts
export async function GET() {
  const checker = new CacheHealthChecker();
  const health = await checker.performHealthCheck();

  return NextResponse.json(health, {
    status: health.status === 'healthy' ? 200 : 503,
  });
}
```

### **6.4 Performance Benchmarking**

**Load Testing Configuration:**
```typescript
// scripts/cache-load-test.ts
import { performance } from 'perf_hooks';

class CacheLoadTester {
  async runLoadTest(domains: string[], concurrency: number, duration: number) {
    console.log(`Starting load test: ${concurrency} concurrent users for ${duration}s`);

    const startTime = performance.now();
    const endTime = startTime + (duration * 1000);
    const results: TestResult[] = [];

    const workers = Array.from({ length: concurrency }, () =>
      this.createWorker(domains, endTime, results)
    );

    await Promise.all(workers);

    return this.analyzeResults(results);
  }

  private async createWorker(
    domains: string[],
    endTime: number,
    results: TestResult[]
  ) {
    while (performance.now() < endTime) {
      const domain = domains[Math.floor(Math.random() * domains.length)];
      const start = performance.now();

      try {
        await getPlatformConfig(domain);
        const duration = performance.now() - start;
        results.push({ success: true, duration, domain });
      } catch (error) {
        const duration = performance.now() - start;
        results.push({ success: false, duration, domain, error: error.message });
      }

      // Random delay between requests
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
    }
  }

  private analyzeResults(results: TestResult[]) {
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    const durations = successful.map(r => r.duration);

    return {
      totalRequests: results.length,
      successfulRequests: successful.length,
      failedRequests: failed.length,
      successRate: (successful.length / results.length) * 100,
      averageResponseTime: durations.reduce((a, b) => a + b, 0) / durations.length,
      p95ResponseTime: this.percentile(durations, 0.95),
      p99ResponseTime: this.percentile(durations, 0.99),
      requestsPerSecond: results.length / (results[results.length - 1].timestamp - results[0].timestamp) * 1000,
    };
  }
}

// Usage
const tester = new CacheLoadTester();
const testDomains = ['domain1.com', 'domain2.com', 'domain3.com'];

// Test current system
const currentResults = await tester.runLoadTest(testDomains, 100, 60);

// Test after improvements
const improvedResults = await tester.runLoadTest(testDomains, 100, 60);

console.log('Performance Comparison:', {
  current: currentResults,
  improved: improvedResults,
  improvement: {
    responseTime: ((currentResults.averageResponseTime - improvedResults.averageResponseTime) / currentResults.averageResponseTime) * 100,
    successRate: improvedResults.successRate - currentResults.successRate,
  }
});
```
