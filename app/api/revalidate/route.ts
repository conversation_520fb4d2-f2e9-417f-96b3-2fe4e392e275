import {revalidatePath, revalidateTag} from 'next/cache';
import {NextRequest, NextResponse} from 'next/server';

// Simple rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function rateLimit(ip: string, limit: number = 10, windowMs: number = 60000): boolean {
  const now = Date.now();
  const key = ip;

  const current = rateLimitStore.get(key);

  if (!current || now > current.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (current.count >= limit) {
    return false;
  }

  current.count++;
  return true;
}

function isValidTag(tag: string): boolean {
  // Only allow specific tag patterns
  const validPatterns = [
    /^platform:[a-zA-Z0-9.-]+$/,     // platform:domain.com
    /^platform:(config|pixel)$/,     // platform:config, platform:pixel
    /^platform$/,                    // platform
    /^product:[a-zA-Z0-9.-]+:[a-zA-Z0-9-]+$/, // product:domain.com:slug
    /^product:[a-zA-Z0-9.-]+$/,      // product:domain.com
    /^product$/,                     // product
    /^reviews:[a-zA-Z0-9.-]+:[a-zA-Z0-9-]+$/, // reviews:domain.com:id
    /^reviews:[a-zA-Z0-9.-]+$/,      // reviews:domain.com
    /^reviews$/,                     // reviews
  ];

  return validPatterns.some(pattern => pattern.test(tag));
}

export async function POST(request: NextRequest) {
  try {
    // 🔒 Authentication
    const authHeader = request.headers.get('authorization');
    const expectedAuth = `Bearer ${process.env.REVALIDATE_SECRET || 'default-secret'}`;

    if (authHeader !== expectedAuth) {
      console.warn(`[CACHE_REVALIDATE] Unauthorized attempt from ${request.ip}`);
      return new Response('Unauthorized', {
        status: 401,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
        }
      });
    }

    // 🔒 Rate limiting
    const clientIP = request.ip || 'unknown';
    if (!rateLimit(clientIP, 10, 60000)) { // 10 requests per minute
      console.warn(`[CACHE_REVALIDATE] Rate limit exceeded for ${clientIP}`);
      return new Response('Rate limit exceeded', {
        status: 429,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
          'Retry-After': '60',
        }
      });
    }

    const body = await request.json();
    const { tag, tags, path } = body;

    // 🔒 Input validation
    if (tag && !isValidTag(tag)) {
      console.warn(`[CACHE_REVALIDATE] Invalid tag: ${tag} from ${clientIP}`);
      return new Response(`Invalid tag: ${tag}`, {
        status: 400,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
        }
      });
    }

    if (tags && Array.isArray(tags)) {
      const invalidTags = tags.filter(t => !isValidTag(t));
      if (invalidTags.length > 0) {
        console.warn(`[CACHE_REVALIDATE] Invalid tags: ${invalidTags.join(', ')} from ${clientIP}`);
        return new Response(`Invalid tags: ${invalidTags.join(', ')}`, {
          status: 400,
          headers: {
            'Cache-Control': 'no-store, no-cache, must-revalidate',
          }
        });
      }
    }

    const revalidatedTags: string[] = [];
    const revalidatedPaths: string[] = [];

    // Selective revalidation
    if (tag) {
      revalidateTag(tag);
      revalidatedTags.push(tag);
    }

    if (tags && Array.isArray(tags)) {
      const validTags = tags.filter(isValidTag);
      validTags.forEach(t => {
        revalidateTag(t);
        revalidatedTags.push(t);
      });
    }

    if (path) {
      revalidatePath(path);
      revalidatedPaths.push(path);
    }

    // 📊 Audit logging
    console.log(`[CACHE_REVALIDATE] ${new Date().toISOString()}: Tags: [${revalidatedTags.join(', ')}], Paths: [${revalidatedPaths.join(', ')}] by ${clientIP}`);

    return NextResponse.json({
      success: true,
      revalidated: true,
      tags: revalidatedTags,
      paths: revalidatedPaths,
      timestamp: Date.now(),
    }, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'CDN-Cache-Control': 'no-store',
      },
    });

  } catch (error) {
    console.error('[CACHE_REVALIDATE_ERROR]', error);
    return new Response('Internal server error', {
      status: 500,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
      }
    });
  }
}

// Keep OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Cache-Control': 'no-store, no-cache, must-revalidate',
    },
  });
}
