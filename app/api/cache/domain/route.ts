import {revalidateTag} from 'next/cache';
import {NextRequest, NextResponse} from 'next/server';

// Import cache functions for warming
import {getPlatformConfig} from '@common/platform/ssr';

// Simple rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function rateLimit(ip: string, limit: number = 20, windowMs: number = 60000): boolean {
  const now = Date.now();
  const key = ip;
  
  const current = rateLimitStore.get(key);
  
  if (!current || now > current.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= limit) {
    return false;
  }
  
  current.count++;
  return true;
}

function isValidDomain(domain: string): boolean {
  // Basic domain validation
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
  return domainRegex.test(domain);
}

async function warmDomainCache(domain: string): Promise<{ success: boolean; details: string }> {
  try {
    // Warm platform config
    await getPlatformConfig(domain);
    
    return {
      success: true,
      details: `Successfully warmed cache for ${domain}`
    };
  } catch (error) {
    console.error(`Cache warming failed for ${domain}:`, error);
    return {
      success: false,
      details: `Cache warming failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    // 🔒 Authentication
    const authHeader = request.headers.get('authorization');
    const expectedAuth = `Bearer ${process.env.REVALIDATE_SECRET || 'default-secret'}`;
    
    if (authHeader !== expectedAuth) {
      console.warn(`[DOMAIN_CACHE] Unauthorized attempt from ${request.ip}`);
      return new Response('Unauthorized', { 
        status: 401,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
        }
      });
    }

    // 🔒 Rate limiting (higher limit for domain operations)
    const clientIP = request.ip || 'unknown';
    if (!rateLimit(clientIP, 20, 60000)) { // 20 requests per minute
      console.warn(`[DOMAIN_CACHE] Rate limit exceeded for ${clientIP}`);
      return new Response('Rate limit exceeded', { 
        status: 429,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
          'Retry-After': '60',
        }
      });
    }

    const body = await request.json();
    const { domain, action, slug, productId } = body;

    // 🔒 Input validation
    if (!domain || !isValidDomain(domain)) {
      console.warn(`[DOMAIN_CACHE] Invalid domain: ${domain} from ${clientIP}`);
      return new Response('Invalid domain', { 
        status: 400,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
        }
      });
    }

    if (!action) {
      return new Response('Action is required', { 
        status: 400,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
        }
      });
    }

    const results: string[] = [];
    const startTime = Date.now();

    switch (action) {
      case 'clear_domain':
        // Clear all cache for specific domain
        await Promise.all([
          revalidateTag(`platform:${domain}`),
          revalidateTag(`product:${domain}`),
          revalidateTag(`reviews:${domain}`),
        ]);
        results.push(`Cleared all cache for ${domain}`);
        break;

      case 'clear_platform':
        // Clear only platform config for domain
        revalidateTag(`platform:${domain}`);
        results.push(`Cleared platform config for ${domain}`);
        break;

      case 'clear_products':
        // Clear all products for domain
        revalidateTag(`product:${domain}`);
        results.push(`Cleared products for ${domain}`);
        break;

      case 'clear_product':
        // Clear specific product
        if (!slug) {
          return new Response('Product slug is required for clear_product action', { 
            status: 400,
            headers: {
              'Cache-Control': 'no-store, no-cache, must-revalidate',
            }
          });
        }
        revalidateTag(`product:${domain}:${slug}`);
        results.push(`Cleared product ${slug} for ${domain}`);
        break;

      case 'clear_reviews':
        // Clear all reviews for domain
        revalidateTag(`reviews:${domain}`);
        results.push(`Cleared reviews for ${domain}`);
        break;

      case 'clear_product_reviews':
        // Clear reviews for specific product
        if (!productId) {
          return new Response('Product ID is required for clear_product_reviews action', { 
            status: 400,
            headers: {
              'Cache-Control': 'no-store, no-cache, must-revalidate',
            }
          });
        }
        revalidateTag(`reviews:${domain}:${productId}`);
        results.push(`Cleared reviews for product ${productId} in ${domain}`);
        break;

      case 'warm_cache':
        // Warm cache for domain
        const warmResult = await warmDomainCache(domain);
        results.push(warmResult.details);
        break;

      default:
        return new Response(`Invalid action: ${action}`, { 
          status: 400,
          headers: {
            'Cache-Control': 'no-store, no-cache, must-revalidate',
          }
        });
    }

    const duration = Date.now() - startTime;

    // 📊 Audit logging
    console.log(`[DOMAIN_CACHE] ${new Date().toISOString()}: ${action} for ${domain} completed in ${duration}ms by ${clientIP}`);

    return NextResponse.json({
      success: true,
      domain,
      action,
      results,
      duration: `${duration}ms`,
      timestamp: Date.now(),
    }, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'CDN-Cache-Control': 'no-store',
      },
    });

  } catch (error) {
    console.error('[DOMAIN_CACHE_ERROR]', error);
    return new Response('Internal server error', { 
      status: 500,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
      }
    });
  }
}

// OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Cache-Control': 'no-store, no-cache, must-revalidate',
    },
  });
}
