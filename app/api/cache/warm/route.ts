import {NextRequest, NextResponse} from 'next/server';
import {globalCacheWarmer} from '@/lib/cache-warming';

// Simple rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function rateLimit(ip: string, limit: number = 5, windowMs: number = 60000): boolean {
  const now = Date.now();
  const key = ip;
  
  const current = rateLimitStore.get(key);
  
  if (!current || now > current.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= limit) {
    return false;
  }
  
  current.count++;
  return true;
}

function isValidDomain(domain: string): boolean {
  const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
  return domainRegex.test(domain);
}

export async function POST(request: NextRequest) {
  try {
    // 🔒 Authentication
    const authHeader = request.headers.get('authorization');
    const expectedAuth = `Bearer ${process.env.REVALIDATE_SECRET || 'default-secret'}`;
    
    if (authHeader !== expectedAuth) {
      console.warn(`[CACHE_WARM] Unauthorized attempt from ${request.ip}`);
      return new Response('Unauthorized', { 
        status: 401,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
        }
      });
    }

    // 🔒 Rate limiting (lower limit for warming operations)
    const clientIP = request.ip || 'unknown';
    if (!rateLimit(clientIP, 5, 60000)) { // 5 requests per minute
      console.warn(`[CACHE_WARM] Rate limit exceeded for ${clientIP}`);
      return new Response('Rate limit exceeded', { 
        status: 429,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
          'Retry-After': '60',
        }
      });
    }

    const body = await request.json();
    const { action, domain, domains } = body;

    if (!action) {
      return new Response('Action is required', { 
        status: 400,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
        }
      });
    }

    const startTime = Date.now();
    let result: any;

    switch (action) {
      case 'warm_all':
        // Warm all popular domains
        console.log(`[CACHE_WARM] Starting warm_all operation from ${clientIP}`);
        result = await globalCacheWarmer.warmPlatformConfigs();
        break;

      case 'warm_domain':
        // Warm specific domain
        if (!domain || !isValidDomain(domain)) {
          return new Response('Valid domain is required for warm_domain action', { 
            status: 400,
            headers: {
              'Cache-Control': 'no-store, no-cache, must-revalidate',
            }
          });
        }
        
        console.log(`[CACHE_WARM] Starting warm_domain for ${domain} from ${clientIP}`);
        result = await globalCacheWarmer.warmDomain(domain);
        break;

      case 'set_popular_domains':
        // Update popular domains list
        if (!domains || !Array.isArray(domains)) {
          return new Response('Domains array is required for set_popular_domains action', { 
            status: 400,
            headers: {
              'Cache-Control': 'no-store, no-cache, must-revalidate',
            }
          });
        }

        const invalidDomains = domains.filter(d => !isValidDomain(d));
        if (invalidDomains.length > 0) {
          return new Response(`Invalid domains: ${invalidDomains.join(', ')}`, { 
            status: 400,
            headers: {
              'Cache-Control': 'no-store, no-cache, must-revalidate',
            }
          });
        }

        console.log(`[CACHE_WARM] Updating popular domains list from ${clientIP}`);
        globalCacheWarmer.setPopularDomains(domains);
        result = {
          message: `Updated popular domains list with ${domains.length} domains`,
          domains: domains,
        };
        break;

      case 'add_popular_domain':
        // Add domain to popular list
        if (!domain || !isValidDomain(domain)) {
          return new Response('Valid domain is required for add_popular_domain action', { 
            status: 400,
            headers: {
              'Cache-Control': 'no-store, no-cache, must-revalidate',
            }
          });
        }

        console.log(`[CACHE_WARM] Adding ${domain} to popular domains from ${clientIP}`);
        globalCacheWarmer.addPopularDomain(domain);
        result = {
          message: `Added ${domain} to popular domains list`,
          domain: domain,
        };
        break;

      case 'remove_popular_domain':
        // Remove domain from popular list
        if (!domain) {
          return new Response('Domain is required for remove_popular_domain action', { 
            status: 400,
            headers: {
              'Cache-Control': 'no-store, no-cache, must-revalidate',
            }
          });
        }

        console.log(`[CACHE_WARM] Removing ${domain} from popular domains from ${clientIP}`);
        globalCacheWarmer.removePopularDomain(domain);
        result = {
          message: `Removed ${domain} from popular domains list`,
          domain: domain,
        };
        break;

      case 'status':
        // Get warming status
        result = globalCacheWarmer.getStatus();
        break;

      default:
        return new Response(`Invalid action: ${action}`, { 
          status: 400,
          headers: {
            'Cache-Control': 'no-store, no-cache, must-revalidate',
          }
        });
    }

    const duration = Date.now() - startTime;

    // 📊 Audit logging
    console.log(`[CACHE_WARM] ${new Date().toISOString()}: ${action} completed in ${duration}ms by ${clientIP}`);

    return NextResponse.json({
      success: true,
      action,
      result,
      duration: `${duration}ms`,
      timestamp: Date.now(),
    }, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'CDN-Cache-Control': 'no-store',
      },
    });

  } catch (error) {
    console.error('[CACHE_WARM_ERROR]', error);
    return new Response('Internal server error', { 
      status: 500,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
      }
    });
  }
}

// GET endpoint for status check
export async function GET(request: NextRequest) {
  try {
    // 🔒 Authentication for GET as well
    const authHeader = request.headers.get('authorization');
    const expectedAuth = `Bearer ${process.env.REVALIDATE_SECRET || 'default-secret'}`;
    
    if (authHeader !== expectedAuth) {
      return new Response('Unauthorized', { 
        status: 401,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
        }
      });
    }

    const status = globalCacheWarmer.getStatus();

    return NextResponse.json({
      success: true,
      status,
      timestamp: Date.now(),
    }, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'CDN-Cache-Control': 'no-store',
      },
    });

  } catch (error) {
    console.error('[CACHE_WARM_STATUS_ERROR]', error);
    return new Response('Internal server error', { 
      status: 500,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
      }
    });
  }
}

// OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Cache-Control': 'no-store, no-cache, must-revalidate',
    },
  });
}
