#!/usr/bin/env tsx

/**
 * Test script for cache APIs
 * Usage: npx tsx scripts/test-cache-api.ts
 */

const API_BASE = process.env.API_BASE || 'http://localhost:3000';
const REVALIDATE_SECRET = process.env.REVALIDATE_SECRET || 'test-secret';

interface ApiResponse {
  success?: boolean;
  error?: string;
  [key: string]: any;
}

async function makeRequest(endpoint: string, data: any): Promise<ApiResponse> {
  try {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${REVALIDATE_SECRET}`,
      },
      body: JSON.stringify(data),
    });

    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${result.error || response.statusText}`);
    }

    return result;
  } catch (error) {
    return {
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

async function testRevalidateAPI() {
  console.log('\n🧪 Testing /api/revalidate...');

  // Test 1: Valid platform tag
  console.log('Test 1: Valid platform tag');
  const result1 = await makeRequest('/api/revalidate', {
    tag: 'platform:example.com'
  });
  console.log('Result:', result1);

  // Test 2: Valid product tag
  console.log('\nTest 2: Valid product tag');
  const result2 = await makeRequest('/api/revalidate', {
    tag: 'product:example.com:test-product'
  });
  console.log('Result:', result2);

  // Test 3: Invalid tag (should fail)
  console.log('\nTest 3: Invalid tag (should fail)');
  const result3 = await makeRequest('/api/revalidate', {
    tag: 'invalid-tag'
  });
  console.log('Result:', result3);

  // Test 4: Multiple tags
  console.log('\nTest 4: Multiple tags');
  const result4 = await makeRequest('/api/revalidate', {
    tags: ['platform:example.com', 'product:example.com']
  });
  console.log('Result:', result4);
}

async function testDomainCacheAPI() {
  console.log('\n🧪 Testing /api/cache/domain...');

  // Test 1: Clear domain cache
  console.log('Test 1: Clear domain cache');
  const result1 = await makeRequest('/api/cache/domain', {
    domain: 'example.com',
    action: 'clear_domain'
  });
  console.log('Result:', result1);

  // Test 2: Clear platform config only
  console.log('\nTest 2: Clear platform config only');
  const result2 = await makeRequest('/api/cache/domain', {
    domain: 'example.com',
    action: 'clear_platform'
  });
  console.log('Result:', result2);

  // Test 3: Clear specific product
  console.log('\nTest 3: Clear specific product');
  const result3 = await makeRequest('/api/cache/domain', {
    domain: 'example.com',
    action: 'clear_product',
    slug: 'test-product'
  });
  console.log('Result:', result3);

  // Test 4: Warm cache
  console.log('\nTest 4: Warm cache');
  const result4 = await makeRequest('/api/cache/domain', {
    domain: 'example.com',
    action: 'warm_cache'
  });
  console.log('Result:', result4);

  // Test 5: Invalid domain (should fail)
  console.log('\nTest 5: Invalid domain (should fail)');
  const result5 = await makeRequest('/api/cache/domain', {
    domain: 'invalid-domain',
    action: 'clear_domain'
  });
  console.log('Result:', result5);

  // Test 6: Invalid action (should fail)
  console.log('\nTest 6: Invalid action (should fail)');
  const result6 = await makeRequest('/api/cache/domain', {
    domain: 'example.com',
    action: 'invalid_action'
  });
  console.log('Result:', result6);
}

async function testAuthenticationFailure() {
  console.log('\n🧪 Testing authentication failure...');

  try {
    const response = await fetch(`${API_BASE}/api/revalidate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer wrong-secret',
      },
      body: JSON.stringify({
        tag: 'platform:example.com'
      }),
    });

    console.log('Status:', response.status);
    console.log('Should be 401 Unauthorized');
  } catch (error) {
    console.log('Error:', error);
  }
}

async function testRateLimit() {
  console.log('\n🧪 Testing rate limiting...');

  console.log('Making 12 rapid requests (should hit rate limit)...');
  
  const promises = Array.from({ length: 12 }, (_, i) => 
    makeRequest('/api/revalidate', {
      tag: `platform:test${i}.com`
    })
  );

  const results = await Promise.all(promises);
  
  const successful = results.filter(r => r.success).length;
  const rateLimited = results.filter(r => r.error?.includes('Rate limit')).length;
  
  console.log(`Successful: ${successful}, Rate limited: ${rateLimited}`);
}

async function main() {
  console.log('🚀 Cache API Test Suite');
  console.log(`API Base: ${API_BASE}`);
  console.log(`Secret: ${REVALIDATE_SECRET ? '[SET]' : '[NOT SET]'}`);

  await testRevalidateAPI();
  await testDomainCacheAPI();
  await testCacheWarmingAPI();
  await testAuthenticationFailure();
  await testRateLimit();

  console.log('\n✅ Test suite completed!');
}

if (require.main === module) {
  main().catch(console.error);
}

async function testCacheWarmingAPI() {
  console.log('\n🧪 Testing /api/cache/warm...');

  // Test 1: Get status
  console.log('Test 1: Get warming status');
  const response1 = await fetch(`${API_BASE}/api/cache/warm`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${REVALIDATE_SECRET}`,
    },
  });
  const result1 = await response1.json();
  console.log('Result:', result1);

  // Test 2: Warm specific domain
  console.log('\nTest 2: Warm specific domain');
  const result2 = await makeRequest('/api/cache/warm', {
    action: 'warm_domain',
    domain: 'example.com'
  });
  console.log('Result:', result2);

  // Test 3: Set popular domains
  console.log('\nTest 3: Set popular domains');
  const result3 = await makeRequest('/api/cache/warm', {
    action: 'set_popular_domains',
    domains: ['example.com', 'test.com']
  });
  console.log('Result:', result3);

  // Test 4: Add popular domain
  console.log('\nTest 4: Add popular domain');
  const result4 = await makeRequest('/api/cache/warm', {
    action: 'add_popular_domain',
    domain: 'new-domain.com'
  });
  console.log('Result:', result4);

  // Test 5: Warm all popular domains
  console.log('\nTest 5: Warm all popular domains');
  const result5 = await makeRequest('/api/cache/warm', {
    action: 'warm_all'
  });
  console.log('Result:', result5);

  // Test 6: Invalid action (should fail)
  console.log('\nTest 6: Invalid action (should fail)');
  const result6 = await makeRequest('/api/cache/warm', {
    action: 'invalid_action'
  });
  console.log('Result:', result6);
}

export { testRevalidateAPI, testDomainCacheAPI, testCacheWarmingAPI, testAuthenticationFailure, testRateLimit };
